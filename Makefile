# Makefile for Operating System Scheduler Simulator
# Compiler and flags for GNU/Linux compatibility

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
TARGET = cpe351
SOURCE = scheduler.cpp

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

# Clean build files
clean:
	rm -f $(TARGET) output.txt

# Run with sample input
test: $(TARGET)
	./$(TARGET) input.txt output.txt

# Display help
help:
	@echo "Available targets:"
	@echo "  all     - Build the scheduler program"
	@echo "  clean   - Remove build files"
	@echo "  test    - Run with sample input"
	@echo "  help    - Show this help message"

.PHONY: all clean test help
