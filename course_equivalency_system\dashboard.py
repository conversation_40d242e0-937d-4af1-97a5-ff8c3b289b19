"""
Interactive Web Dashboard for Course Equivalency System
Built with Streamlit for easy deployment and user interaction
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import numpy as np
from course_matcher import CourseEquivalencyMatcher
from data_scraper import UniversityCourseScraper
import os

# Page configuration
st.set_page_config(
    page_title="University Course Equivalency System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
    }
    .university-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin: 0.5rem 0;
    }
    .metric-card {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2E86AB;
    }
    .similarity-high { color: #28a745; font-weight: bold; }
    .similarity-medium { color: #ffc107; font-weight: bold; }
    .similarity-low { color: #dc3545; font-weight: bold; }
</style>
""", unsafe_allow_html=True)

class CourseEquivalencyDashboard:
    def __init__(self):
        self.matcher = CourseEquivalencyMatcher()
        self.universities = ['CIU', 'EMU', 'NEAR_EAST']
        self.programs = ['computer_engineering', 'artificial_intelligence']
        
    def load_data(self):
        """Load course data and matches"""
        try:
            if os.path.exists('university_courses.csv'):
                courses_df = pd.read_csv('university_courses.csv')
            else:
                courses_df = pd.DataFrame()
                
            if os.path.exists('course_matches.csv'):
                matches_df = pd.read_csv('course_matches.csv')
            else:
                matches_df = pd.DataFrame()
                
            return courses_df, matches_df
        except Exception as e:
            st.error(f"Error loading data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def render_header(self):
        """Render the main header"""
        st.markdown('<h1 class="main-header">🎓 University Course Equivalency System</h1>', 
                   unsafe_allow_html=True)
        st.markdown("### Analyze and match courses between CIU, EMU, and Near East University")
        st.markdown("---")
    
    def render_sidebar(self):
        """Render the sidebar with controls"""
        st.sidebar.title("🔧 Control Panel")
        
        # Data collection section
        st.sidebar.header("📊 Data Collection")
        if st.sidebar.button("🔄 Scrape Fresh Data", help="Collect latest course data from universities"):
            with st.spinner("Scraping course data..."):
                scraper = UniversityCourseScraper()
                try:
                    courses = scraper.scrape_all_universities()
                    df = scraper.save_to_csv(courses)
                    scraper.close()
                    st.sidebar.success(f"✅ Scraped {len(courses)} courses!")
                    st.experimental_rerun()
                except Exception as e:
                    st.sidebar.error(f"❌ Scraping failed: {e}")
        
        # Analysis section
        st.sidebar.header("🔍 Analysis")
        similarity_threshold = st.sidebar.slider(
            "Minimum Similarity Threshold", 
            min_value=0.5, max_value=1.0, value=0.6, step=0.05
        )
        
        if st.sidebar.button("🚀 Run Analysis", help="Analyze course equivalencies"):
            return self.run_analysis(similarity_threshold)
        
        return similarity_threshold
    
    def run_analysis(self, threshold):
        """Run the course equivalency analysis"""
        courses_df, _ = self.load_data()
        
        if courses_df.empty:
            st.sidebar.error("❌ No course data found. Please scrape data first.")
            return threshold
        
        with st.spinner("Analyzing course equivalencies..."):
            try:
                matches_df = self.matcher.find_course_matches(courses_df, min_similarity=threshold)
                self.matcher.save_matches(matches_df)
                self.matcher.save_matches_json(matches_df)
                st.sidebar.success(f"✅ Found {len(matches_df)} matches!")
                st.experimental_rerun()
            except Exception as e:
                st.sidebar.error(f"❌ Analysis failed: {e}")
        
        return threshold
    
    def render_overview(self, courses_df, matches_df):
        """Render overview statistics"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown('<div class="metric-card">', unsafe_allow_html=True)
            st.metric("Total Courses", len(courses_df))
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col2:
            st.markdown('<div class="metric-card">', unsafe_allow_html=True)
            st.metric("Course Matches", len(matches_df))
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col3:
            st.markdown('<div class="metric-card">', unsafe_allow_html=True)
            universities_count = courses_df['university'].nunique() if not courses_df.empty else 0
            st.metric("Universities", universities_count)
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col4:
            st.markdown('<div class="metric-card">', unsafe_allow_html=True)
            programs_count = courses_df['program'].nunique() if not courses_df.empty else 0
            st.metric("Programs", programs_count)
            st.markdown('</div>', unsafe_allow_html=True)
    
    def render_university_comparison(self, courses_df):
        """Render university comparison charts"""
        if courses_df.empty:
            st.warning("📊 No course data available for visualization")
            return
        
        st.header("🏫 University Comparison")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Courses by university
            uni_counts = courses_df['university'].value_counts()
            fig_uni = px.bar(
                x=uni_counts.index, y=uni_counts.values,
                title="Courses by University",
                labels={'x': 'University', 'y': 'Number of Courses'},
                color=uni_counts.values,
                color_continuous_scale='viridis'
            )
            st.plotly_chart(fig_uni, use_container_width=True)
        
        with col2:
            # Courses by program
            prog_counts = courses_df['program'].value_counts()
            fig_prog = px.pie(
                values=prog_counts.values, names=prog_counts.index,
                title="Courses by Program"
            )
            st.plotly_chart(fig_prog, use_container_width=True)
    
    def render_matches_analysis(self, matches_df):
        """Render matches analysis"""
        if matches_df.empty:
            st.warning("🔍 No course matches found. Try running the analysis first.")
            return
        
        st.header("🔗 Course Matches Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Confidence distribution
            confidence_counts = matches_df['confidence'].value_counts()
            fig_conf = px.bar(
                x=confidence_counts.index, y=confidence_counts.values,
                title="Match Confidence Distribution",
                labels={'x': 'Confidence Level', 'y': 'Number of Matches'},
                color=confidence_counts.values,
                color_continuous_scale='RdYlGn'
            )
            st.plotly_chart(fig_conf, use_container_width=True)
        
        with col2:
            # Similarity score distribution
            fig_sim = px.histogram(
                matches_df, x='overall_similarity',
                title="Similarity Score Distribution",
                nbins=20,
                labels={'overall_similarity': 'Similarity Score', 'count': 'Frequency'}
            )
            st.plotly_chart(fig_sim, use_container_width=True)
    
    def render_detailed_matches(self, matches_df):
        """Render detailed matches table"""
        if matches_df.empty:
            return
        
        st.header("📋 Detailed Course Matches")
        
        # Filters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            selected_uni1 = st.selectbox(
                "University 1", 
                options=['All'] + list(matches_df['university1'].unique())
            )
        
        with col2:
            selected_uni2 = st.selectbox(
                "University 2", 
                options=['All'] + list(matches_df['university2'].unique())
            )
        
        with col3:
            selected_confidence = st.selectbox(
                "Confidence Level",
                options=['All'] + list(matches_df['confidence'].unique())
            )
        
        # Filter data
        filtered_df = matches_df.copy()
        
        if selected_uni1 != 'All':
            filtered_df = filtered_df[filtered_df['university1'] == selected_uni1]
        
        if selected_uni2 != 'All':
            filtered_df = filtered_df[filtered_df['university2'] == selected_uni2]
        
        if selected_confidence != 'All':
            filtered_df = filtered_df[filtered_df['confidence'] == selected_confidence]
        
        # Display table
        if not filtered_df.empty:
            # Format similarity scores
            filtered_df['similarity_formatted'] = filtered_df['overall_similarity'].apply(
                lambda x: f"{x:.2%}"
            )
            
            # Select columns to display
            display_columns = [
                'university1', 'course1_code', 'course1_name', 'course1_credits',
                'university2', 'course2_code', 'course2_name', 'course2_credits',
                'similarity_formatted', 'confidence', 'recommendation'
            ]
            
            st.dataframe(
                filtered_df[display_columns],
                use_container_width=True,
                height=400
            )
            
            # Download button
            csv = filtered_df.to_csv(index=False)
            st.download_button(
                label="📥 Download Filtered Results",
                data=csv,
                file_name="course_matches_filtered.csv",
                mime="text/csv"
            )
        else:
            st.info("No matches found with the selected filters.")
    
    def render_transfer_planner(self, matches_df):
        """Render transfer planning tool"""
        st.header("🎯 Transfer Planning Tool")
        
        if matches_df.empty:
            st.warning("Please run the analysis first to use the transfer planner.")
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            source_uni = st.selectbox(
                "Source University",
                options=self.universities
            )
        
        with col2:
            target_uni = st.selectbox(
                "Target University",
                options=[uni for uni in self.universities if uni != source_uni]
            )
        
        # Course input
        st.subheader("Enter Your Courses")
        course_input = st.text_area(
            "Enter course codes (one per line):",
            placeholder="COMP101\nMATH201\nENG102"
        )
        
        if st.button("📊 Generate Transfer Plan") and course_input:
            student_courses = [course.strip() for course in course_input.split('\n') if course.strip()]
            
            transfer_plan = self.matcher.generate_transfer_plan(
                matches_df, source_uni, target_uni, student_courses
            )
            
            # Display results
            st.subheader("📋 Transfer Plan Results")
            
            # Summary
            summary = transfer_plan['summary']
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Courses", summary['total_courses'])
            with col2:
                st.metric("Transferable", summary['transferable_courses'])
            with col3:
                st.metric("Original Credits", summary['original_credits'])
            with col4:
                st.metric("Credit Loss", summary['credit_loss'])
            
            # Detailed plan
            if transfer_plan['transfer_plan']:
                plan_df = pd.DataFrame(transfer_plan['transfer_plan'])
                st.dataframe(plan_df, use_container_width=True)
            else:
                st.warning("No transferable courses found for the entered course codes.")
    
    def run(self):
        """Main dashboard function"""
        self.render_header()
        
        # Sidebar
        similarity_threshold = self.render_sidebar()
        
        # Load data
        courses_df, matches_df = self.load_data()
        
        # Main content
        self.render_overview(courses_df, matches_df)
        
        # Create tabs
        tab1, tab2, tab3, tab4 = st.tabs([
            "📊 Overview", "🔗 Matches Analysis", "📋 Detailed Results", "🎯 Transfer Planner"
        ])
        
        with tab1:
            self.render_university_comparison(courses_df)
        
        with tab2:
            self.render_matches_analysis(matches_df)
        
        with tab3:
            self.render_detailed_matches(matches_df)
        
        with tab4:
            self.render_transfer_planner(matches_df)

if __name__ == "__main__":
    dashboard = CourseEquivalencyDashboard()
    dashboard.run()
