"""
Quick Demo Script for Course Equivalency System
Demonstrates the key features with sample data
"""

import os
import sys
import pandas as pd
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from sample_data import save_sample_data
from course_matcher import CourseEquivalencyMatcher

def run_demo():
    """Run a complete demonstration of the system"""
    
    print("🎓 UNIVERSITY COURSE EQUIVALENCY SYSTEM DEMO")
    print("=" * 60)
    
    # Step 1: Generate sample data
    print("\n📊 Step 1: Generating Sample Course Data...")
    df = save_sample_data()
    print(f"✅ Generated {len(df)} courses across 3 universities and 2 programs")
    
    # Display sample courses
    print("\n📋 Sample Courses:")
    sample_courses = df.head(6)
    for _, course in sample_courses.iterrows():
        print(f"   {course['university']}: {course['course_code']} - {course['course_name']} ({course['credits']} credits)")
    
    # Step 2: Initialize matcher and analyze
    print("\n🔍 Step 2: Analyzing Course Equivalencies...")
    matcher = CourseEquivalencyMatcher()
    
    # Find matches with different thresholds
    matches_df = matcher.find_course_matches(df, min_similarity=0.6)
    print(f"✅ Found {len(matches_df)} potential course matches")
    
    # Step 3: Display analysis results
    print("\n📈 Step 3: Analysis Results")
    print("-" * 40)
    
    if not matches_df.empty:
        # Confidence distribution
        print("\n🎯 Confidence Distribution:")
        confidence_counts = matches_df['confidence'].value_counts()
        for confidence, count in confidence_counts.items():
            print(f"   {confidence}: {count} matches")
        
        # Top matches
        print("\n🏆 Top 5 Course Matches:")
        top_matches = matches_df.nlargest(5, 'overall_similarity')
        for i, (_, match) in enumerate(top_matches.iterrows(), 1):
            print(f"\n   {i}. {match['course1_code']} ({match['university1']}) ↔ {match['course2_code']} ({match['university2']})")
            print(f"      Course 1: {match['course1_name']}")
            print(f"      Course 2: {match['course2_name']}")
            print(f"      Similarity: {match['overall_similarity']:.1%}")
            print(f"      Confidence: {match['confidence']}")
            print(f"      Recommendation: {match['recommendation']}")
        
        # Step 4: Generate sample transfer plan
        print("\n🎯 Step 4: Sample Transfer Plan")
        print("-" * 40)
        
        # Sample student courses from CIU
        sample_student_courses = ['COMP101', 'MATH101', 'COMP201', 'AI101']
        
        print(f"\n📚 Student's Current Courses (CIU): {', '.join(sample_student_courses)}")
        print("🔄 Generating transfer plan to EMU...")
        
        transfer_plan = matcher.generate_transfer_plan(
            matches_df, 'CIU', 'EMU', sample_student_courses
        )
        
        # Display transfer plan
        summary = transfer_plan['summary']
        print(f"\n📊 Transfer Summary:")
        print(f"   Total Courses: {summary['total_courses']}")
        print(f"   Transferable: {summary['transferable_courses']}")
        print(f"   Original Credits: {summary['original_credits']}")
        print(f"   Transferred Credits: {summary['transferred_credits']}")
        print(f"   Credit Loss: {summary['credit_loss']}")
        
        if transfer_plan['transfer_plan']:
            print(f"\n📋 Detailed Transfer Plan:")
            for i, course in enumerate(transfer_plan['transfer_plan'], 1):
                print(f"\n   {i}. {course['original_course']} → {course['transfer_to']}")
                print(f"      Credits: {course['original_credits']} → {course['transfer_credits']}")
                print(f"      Similarity: {course['similarity']:.1%}")
                print(f"      Confidence: {course['confidence']}")
        
        # Step 5: Save results
        print("\n💾 Step 5: Saving Results...")
        matcher.save_matches(matches_df, 'demo_course_matches.csv')
        matcher.save_matches_json(matches_df, 'demo_course_matches.json')
        print("✅ Results saved to demo_course_matches.csv and demo_course_matches.json")
        
        # Step 6: University comparison
        print("\n🏫 Step 6: University Comparison")
        print("-" * 40)
        
        print("\n📊 Courses by University:")
        uni_counts = df['university'].value_counts()
        for uni, count in uni_counts.items():
            print(f"   {uni}: {count} courses")
        
        print("\n📚 Courses by Program:")
        prog_counts = df['program'].value_counts()
        for prog, count in prog_counts.items():
            print(f"   {prog.replace('_', ' ').title()}: {count} courses")
        
        # Program-specific matches
        print("\n🔗 Matches by Program:")
        if 'program' in matches_df.columns:
            prog_matches = matches_df['program'].value_counts()
            for prog, count in prog_matches.items():
                print(f"   {prog.replace('_', ' ').title()}: {count} matches")
    
    else:
        print("❌ No matches found. Try lowering the similarity threshold.")
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 DEMO COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\n📁 Generated Files:")
    print("   - university_courses.csv (sample course data)")
    print("   - demo_course_matches.csv (analysis results)")
    print("   - demo_course_matches.json (analysis results in JSON)")
    
    print("\n🚀 Next Steps:")
    print("   1. Run 'streamlit run dashboard.py' to launch the web interface")
    print("   2. Try 'python main.py --mode dashboard' for the full application")
    print("   3. Experiment with different similarity thresholds")
    print("   4. Test with real university data using scraping mode")
    
    print("\n📖 For more information, check README.md")

def quick_test():
    """Quick functionality test"""
    print("🧪 Running Quick Functionality Test...")
    
    # Test course similarity calculation
    matcher = CourseEquivalencyMatcher()
    
    course1 = {
        'course_code': 'COMP101',
        'course_name': 'Introduction to Programming',
        'credits': 3,
        'description': 'Basic programming concepts using Python and C++'
    }
    
    course2 = {
        'course_code': 'CSE101',
        'course_name': 'Programming Fundamentals',
        'credits': 3,
        'description': 'Fundamentals of computer programming and problem solving'
    }
    
    similarity, details = matcher.calculate_course_similarity(course1, course2)
    
    print(f"\n🔍 Similarity Test:")
    print(f"   Course 1: {course1['course_code']} - {course1['course_name']}")
    print(f"   Course 2: {course2['course_code']} - {course2['course_name']}")
    print(f"   Overall Similarity: {similarity:.1%}")
    print(f"   Details: {details}")
    
    confidence = matcher.get_confidence_level(similarity)
    recommendation = matcher.get_recommendation(similarity, details)
    
    print(f"   Confidence: {confidence}")
    print(f"   Recommendation: {recommendation}")
    
    print("✅ Quick test completed successfully!")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Course Equivalency System Demo')
    parser.add_argument('--quick', action='store_true', help='Run quick functionality test only')
    
    args = parser.parse_args()
    
    if args.quick:
        quick_test()
    else:
        run_demo()
