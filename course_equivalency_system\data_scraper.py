"""
University Course Data Scraper
Scrapes course information from CIU, EMU, and Near East University websites
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import json
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UniversityCourseScraper:
    def __init__(self):
        self.universities = {
            'CIU': {
                'name': 'Cyprus International University',
                'base_url': 'https://www.ciu.edu.tr',
                'programs': {
                    'computer_engineering': '/en/academic/faculties/engineering/computer-engineering',
                    'artificial_intelligence': '/en/academic/faculties/engineering/artificial-intelligence'
                }
            },
            'EMU': {
                'name': 'Eastern Mediterranean University',
                'base_url': 'https://www.emu.edu.tr',
                'programs': {
                    'computer_engineering': '/en/academic/faculties/engineering/computer-engineering',
                    'artificial_intelligence': '/en/academic/faculties/engineering/artificial-intelligence'
                }
            },
            'NEAR_EAST': {
                'name': 'Near East University',
                'base_url': 'https://www.neu.edu.tr',
                'programs': {
                    'computer_engineering': '/en/academic/faculties/engineering/computer-engineering',
                    'artificial_intelligence': '/en/academic/faculties/engineering/artificial-intelligence'
                }
            }
        }
        self.setup_driver()
        
    def setup_driver(self):
        """Setup Chrome driver with options"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            self.driver = None
    
    def scrape_ciu_courses(self, program):
        """Scrape courses from CIU website"""
        courses = []
        try:
            # CIU specific scraping logic
            base_url = self.universities['CIU']['base_url']
            program_url = base_url + self.universities['CIU']['programs'][program]
            
            response = requests.get(program_url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for course tables or course listings
            course_elements = soup.find_all(['table', 'div'], class_=re.compile(r'course|curriculum|syllabus', re.I))
            
            for element in course_elements:
                course_rows = element.find_all('tr') if element.name == 'table' else element.find_all('div')
                
                for row in course_rows:
                    course_data = self.extract_course_info(row, 'CIU')
                    if course_data:
                        course_data['university'] = 'CIU'
                        course_data['program'] = program
                        courses.append(course_data)
                        
        except Exception as e:
            logger.error(f"Error scraping CIU {program}: {e}")
            
        return courses
    
    def scrape_emu_courses(self, program):
        """Scrape courses from EMU website"""
        courses = []
        try:
            # EMU specific scraping logic
            base_url = self.universities['EMU']['base_url']
            program_url = base_url + self.universities['EMU']['programs'][program]
            
            if self.driver:
                self.driver.get(program_url)
                time.sleep(3)
                
                # Look for course information
                course_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    'table, .course-list, .curriculum, .syllabus')
                
                for element in course_elements:
                    course_data = self.extract_course_info_selenium(element, 'EMU')
                    if course_data:
                        course_data['university'] = 'EMU'
                        course_data['program'] = program
                        courses.append(course_data)
                        
        except Exception as e:
            logger.error(f"Error scraping EMU {program}: {e}")
            
        return courses
    
    def scrape_near_east_courses(self, program):
        """Scrape courses from Near East University website"""
        courses = []
        try:
            # Near East specific scraping logic
            base_url = self.universities['NEAR_EAST']['base_url']
            program_url = base_url + self.universities['NEAR_EAST']['programs'][program]
            
            response = requests.get(program_url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for course information
            course_elements = soup.find_all(['table', 'div'], class_=re.compile(r'course|curriculum|program', re.I))
            
            for element in course_elements:
                course_data = self.extract_course_info(element, 'NEAR_EAST')
                if course_data:
                    course_data['university'] = 'NEAR_EAST'
                    course_data['program'] = program
                    courses.append(course_data)
                    
        except Exception as e:
            logger.error(f"Error scraping Near East {program}: {e}")
            
        return courses
    
    def extract_course_info(self, element, university):
        """Extract course information from HTML element"""
        try:
            course_data = {}
            text = element.get_text(strip=True)
            
            # Extract course code (usually starts with letters followed by numbers)
            code_match = re.search(r'([A-Z]{2,4}\s*\d{3,4})', text)
            if code_match:
                course_data['course_code'] = code_match.group(1)
            
            # Extract course name (usually after course code)
            name_match = re.search(r'([A-Z]{2,4}\s*\d{3,4})\s*[-:]?\s*([^0-9]+?)(?:\s*\d+\s*credits?|\s*\(\d+\)|\s*$)', text, re.I)
            if name_match:
                course_data['course_name'] = name_match.group(2).strip()
            
            # Extract credits
            credit_match = re.search(r'(\d+)\s*credits?|\((\d+)\)', text, re.I)
            if credit_match:
                course_data['credits'] = int(credit_match.group(1) or credit_match.group(2))
            
            # Extract description if available
            if len(text) > 50:
                course_data['description'] = text[:200] + "..." if len(text) > 200 else text
            
            return course_data if 'course_code' in course_data else None
            
        except Exception as e:
            logger.error(f"Error extracting course info: {e}")
            return None
    
    def extract_course_info_selenium(self, element, university):
        """Extract course information using Selenium WebElement"""
        try:
            course_data = {}
            text = element.text
            
            # Similar extraction logic as above but for Selenium elements
            code_match = re.search(r'([A-Z]{2,4}\s*\d{3,4})', text)
            if code_match:
                course_data['course_code'] = code_match.group(1)
            
            name_match = re.search(r'([A-Z]{2,4}\s*\d{3,4})\s*[-:]?\s*([^0-9]+?)(?:\s*\d+\s*credits?|\s*\(\d+\)|\s*$)', text, re.I)
            if name_match:
                course_data['course_name'] = name_match.group(2).strip()
            
            credit_match = re.search(r'(\d+)\s*credits?|\((\d+)\)', text, re.I)
            if credit_match:
                course_data['credits'] = int(credit_match.group(1) or credit_match.group(2))
            
            if len(text) > 50:
                course_data['description'] = text[:200] + "..." if len(text) > 200 else text
            
            return course_data if 'course_code' in course_data else None
            
        except Exception as e:
            logger.error(f"Error extracting course info with Selenium: {e}")
            return None
    
    def scrape_all_universities(self):
        """Scrape courses from all universities"""
        all_courses = []
        
        for program in ['computer_engineering', 'artificial_intelligence']:
            logger.info(f"Scraping {program} courses...")
            
            # Scrape CIU
            logger.info("Scraping CIU...")
            ciu_courses = self.scrape_ciu_courses(program)
            all_courses.extend(ciu_courses)
            time.sleep(2)
            
            # Scrape EMU
            logger.info("Scraping EMU...")
            emu_courses = self.scrape_emu_courses(program)
            all_courses.extend(emu_courses)
            time.sleep(2)
            
            # Scrape Near East
            logger.info("Scraping Near East...")
            near_east_courses = self.scrape_near_east_courses(program)
            all_courses.extend(near_east_courses)
            time.sleep(2)
        
        return all_courses
    
    def save_to_csv(self, courses, filename='university_courses.csv'):
        """Save courses to CSV file"""
        df = pd.DataFrame(courses)
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(courses)} courses to {filename}")
        return df
    
    def save_to_json(self, courses, filename='university_courses.json'):
        """Save courses to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(courses, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved {len(courses)} courses to {filename}")
    
    def close(self):
        """Close the driver"""
        if self.driver:
            self.driver.quit()

if __name__ == "__main__":
    scraper = UniversityCourseScraper()
    
    try:
        # Scrape all courses
        courses = scraper.scrape_all_universities()
        
        # Save to files
        df = scraper.save_to_csv(courses)
        scraper.save_to_json(courses)
        
        print(f"Successfully scraped {len(courses)} courses")
        print(f"Universities: {df['university'].value_counts().to_dict()}")
        print(f"Programs: {df['program'].value_counts().to_dict()}")
        
    finally:
        scraper.close()
