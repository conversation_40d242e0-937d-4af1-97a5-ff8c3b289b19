/*
 * Operating System Multiple Queue Scheduler Simulator
 * Author: [Your Name]
 * Date: July 25, 2025
 * 
 * This program simulates three different CPU scheduling algorithms:
 * 1. First Come First Serve (FCFS)
 * 2. Shortest Job First (SJF) - Non-preemptive
 * 3. Priority Scheduling - Non-preemptive
 * 
 * The program reads process data from an input file and outputs
 * waiting times and average waiting times for each algorithm.
 */

#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <sstream>
#include <algorithm>
#include <iomanip>

using namespace std;

// Structure to represent a process
// Using struct instead of arrays as per requirements
struct Process {
    int burstTime;      // Time required for process execution
    int priority;       // Process priority (lower number = higher priority)
    int queueId;        // Queue identifier
    int waitingTime;    // Calculated waiting time
    int processId;      // Process identifier for tracking
    
    // Constructor for easy initialization
    Process(int bt, int pr, int qid, int pid) 
        : burstTime(bt), priority(pr), queueId(qid), processId(pid), waitingTime(0) {}
};

// Class to handle the scheduling simulation
class SchedulerSimulator {
private:
    vector<Process> processes;  // Using vector instead of array
    string inputFileName;
    string outputFileName;
    
public:
    // Constructor
    SchedulerSimulator(const string& inputFile, const string& outputFile) 
        : inputFileName(inputFile), outputFileName(outputFile) {}
    
    // Function declarations - you'll implement these
    bool readInputFile();
    void simulateFCFS(vector<Process>& processList);
    void simulateSJF(vector<Process>& processList);
    void simulatePriority(vector<Process>& processList);
    void writeOutput();
    void displayResults();
    
    // Helper functions
    vector<Process> getProcessesByQueue(int queueId);
    double calculateAverageWaitingTime(const vector<Process>& processList);
    void resetWaitingTimes(vector<Process>& processList);
};

// Main function - entry point of the program
int main(int argc, char* argv[]) {
    // Check command line arguments
    if (argc != 3) {
        cout << "Usage: " << argv[0] << " <input_file> <output_file>" << endl;
        cout << "Example: ./cpe351 input.txt output.txt" << endl;
        return 1;
    }
    
    // Create scheduler simulator instance
    SchedulerSimulator simulator(argv[1], argv[2]);
    
    // Read input file
    if (!simulator.readInputFile()) {
        cerr << "Error: Could not read input file: " << argv[1] << endl;
        return 1;
    }
    
    // Display results to screen and write to output file
    simulator.displayResults();
    simulator.writeOutput();
    
    cout << "Simulation completed successfully!" << endl;
    cout << "Results written to: " << argv[2] << endl;
    
    return 0;
}

// TODO: Implement the following functions
// 1. readInputFile() - Parse colon-delimited input
// 2. simulateFCFS() - First Come First Serve algorithm
// 3. simulateSJF() - Shortest Job First algorithm  
// 4. simulatePriority() - Priority scheduling algorithm
// 5. writeOutput() - Format and write results
// 6. Helper functions for calculations

/*
 * Implementation Guidelines:
 * 
 * 1. readInputFile():
 *    - Open input file and read line by line
 *    - Split each line by ':' character
 *    - Create Process objects and add to processes vector
 *    - Handle file errors gracefully
 * 
 * 2. Scheduling Algorithms:
 *    - FCFS: Process in arrival order (order in file)
 *    - SJF: Sort by burst time, process shortest first
 *    - Priority: Sort by priority, process highest priority first
 *    - Calculate waiting time for each process
 * 
 * 3. Waiting Time Calculation:
 *    - For each process, waiting time = sum of burst times of all previous processes
 *    - First process always has waiting time = 0
 * 
 * 4. Output Format:
 *    - QueueID:Algorithm:WT1:WT2:...:WTn:AWT
 *    - Algorithm codes: 1=FCFS, 2=SJF, 3=Priority
 */
