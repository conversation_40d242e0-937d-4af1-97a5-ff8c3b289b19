{"food_items": [{"item_id": "F001", "name": "Margherita Pizza", "price": 12.99, "category": "Pizza", "stock": 18, "description": "Classic pizza with tomato sauce, mozzarella, and basil", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F002", "name": "Chicken Burger", "price": 8.99, "category": "Burgers", "stock": 15, "description": "Grilled chicken breast with lettuce, tomato, and mayo", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F003", "name": "<PERSON>", "price": 7.99, "category": "Salads", "stock": 10, "description": "Fresh romaine lettuce with Caesar dressing and croutons", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F004", "name": "Spaghetti Carbonara", "price": 11.99, "category": "Pasta", "stock": 12, "description": "Creamy pasta with bacon, eggs, and parmesan cheese", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F005", "name": "Fish & Chips", "price": 13.99, "category": "Main Course", "stock": 8, "description": "Battered fish with crispy fries and tartar sauce", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F006", "name": "Chocolate Cake", "price": 5.99, "category": "Desserts", "stock": 6, "description": "Rich chocolate cake with chocolate frosting", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F007", "name": "Fresh Orange Juice", "price": 3.99, "category": "Beverages", "stock": 25, "description": "Freshly squeezed orange juice", "created_date": "2025-07-26T16:04:37.217788"}, {"item_id": "F008", "name": "Cappuccino", "price": 4.49, "category": "Beverages", "stock": 27, "description": "Italian coffee with steamed milk foam", "created_date": "2025-07-26T16:04:37.217788"}], "orders": [{"order_id": "ORD0001", "customer_name": "<PERSON><PERSON><PERSON>", "customer_phone": "+90-************", "items": [{"item_id": "F001", "quantity": 2, "price": 12.99, "subtotal": 25.98}, {"item_id": "F008", "quantity": 3, "price": 4.49, "subtotal": 13.47}], "total_amount": 39.45, "status": "pending", "order_date": "2025-07-26T16:06:07.687630", "delivery_address": "Cyprus International University", "notes": "Please deliver to Computer Engineering Department"}]}