"""
Python Dictionaries - Complete Guide with Examples
==================================================

A dictionary is a collection of key-value pairs that is:
- Unordered (Python 3.7+ maintains insertion order)
- Mutable (can be changed after creation)
- Indexed by keys (not by position like lists)
- Does not allow duplicate keys
"""

# ============================================================================
# 1. CREATING DICTIONARIES
# ============================================================================

def creating_dictionaries():
    """Different ways to create dictionaries"""
    print("=== CREATING DICTIONARIES ===\n")

    # Method 1: Using curly braces {}
    student = {
        "name": "Fadhl Alsharif",
        "age": 22,
        "university": "CIU",
        "major": "AI Engineering",
        "gpa": 3.8
    }
    print("1. Using curly braces:")
    print(f"   {student}\n")

    # Method 2: Using dict() constructor
    course = dict(
        code="AI301",
        name="Machine Learning",
        credits=4,
        instructor="Dr. Smith"
    )
    print("2. Using dict() constructor:")
    print(f"   {course}\n")

    # Method 3: From list of tuples
    grades = dict([("Math", 95), ("Physics", 88), ("Programming", 92)])
    print("3. From list of tuples:")
    print(f"   {grades}\n")

    # Method 4: Using dictionary comprehension
    squares = {x: x**2 for x in range(1, 6)}
    print("4. Dictionary comprehension:")
    print(f"   {squares}\n")

    # Method 5: Empty dictionary
    empty_dict1 = {}
    empty_dict2 = dict()
    print("5. Empty dictionaries:")
    print(f"   empty_dict1: {empty_dict1}")
    print(f"   empty_dict2: {empty_dict2}\n")

# ============================================================================
# 2. ACCESSING DICTIONARY ELEMENTS
# ============================================================================

def accessing_elements():
    """How to access dictionary values"""
    print("=== ACCESSING DICTIONARY ELEMENTS ===\n")

    student = {
        "name": "Fadhl Alsharif",
        "age": 22,
        "university": "CIU",
        "courses": ["AI", "ML", "Data Science"],
        "contact": {
            "email": "<EMAIL>",
            "phone": "+90-************"
        }
    }

    # Method 1: Using square brackets []
    print("1. Using square brackets:")
    print(f"   Name: {student['name']}")
    print(f"   Age: {student['age']}\n")

    # Method 2: Using get() method (safer)
    print("2. Using get() method:")
    print(f"   University: {student.get('university')}")
    print(f"   GPA: {student.get('gpa', 'Not available')}")  # Default value
    print()

    # Accessing nested dictionaries
    print("3. Accessing nested elements:")
    print(f"   Email: {student['contact']['email']}")
    print(f"   First course: {student['courses'][0]}\n")

    # Error handling
    print("4. Error handling:")
    try:
        print(student['salary'])  # This will raise KeyError
    except KeyError as e:
        print(f"   KeyError: {e}")

    # Safe access with get()
    salary = student.get('salary', 'Not specified')
    print(f"   Salary (using get): {salary}\n")

# ============================================================================
# 3. MODIFYING DICTIONARIES
# ============================================================================

def modifying_dictionaries():
    """How to modify dictionary contents"""
    print("=== MODIFYING DICTIONARIES ===\n")

    student = {"name": "Fadhl", "age": 22, "major": "AI"}
    print(f"Original: {student}")

    # Adding new key-value pairs
    student["university"] = "CIU"
    student["gpa"] = 3.8
    print(f"After adding: {student}")

    # Updating existing values
    student["age"] = 23
    student["major"] = "AI Engineering"
    print(f"After updating: {student}")

    # Using update() method
    new_info = {"graduation_year": 2026, "scholarship": True}
    student.update(new_info)
    print(f"After update(): {student}")

    # Removing elements
    print("\nRemoving elements:")

    # Method 1: del statement
    del student["scholarship"]
    print(f"After del: {student}")

    # Method 2: pop() method (returns the value)
    gpa = student.pop("gpa")
    print(f"Popped GPA: {gpa}")
    print(f"After pop(): {student}")

    # Method 3: popitem() (removes last item in Python 3.7+)
    last_item = student.popitem()
    print(f"Popped item: {last_item}")
    print(f"After popitem(): {student}")

    # Method 4: clear() (removes all items)
    temp_dict = {"a": 1, "b": 2}
    temp_dict.clear()
    print(f"After clear(): {temp_dict}\n")

# ============================================================================
# 4. DICTIONARY METHODS
# ============================================================================

def dictionary_methods():
    """Comprehensive overview of dictionary methods"""
    print("=== DICTIONARY METHODS ===\n")

    courses = {
        "AI301": {"name": "Machine Learning", "credits": 4, "grade": "A"},
        "CS201": {"name": "Data Structures", "credits": 3, "grade": "B+"},
        "MATH301": {"name": "Statistics", "credits": 3, "grade": "A-"},
        "ENG101": {"name": "Technical Writing", "credits": 2, "grade": "B"}
    }

    print("Sample dictionary:")
    for code, info in courses.items():
        print(f"   {code}: {info}")
    print()

    # keys() method
    print("1. keys() - Get all keys:")
    keys = courses.keys()
    print(f"   {list(keys)}\n")

    # values() method
    print("2. values() - Get all values:")
    values = courses.values()
    print(f"   {list(values)}\n")

    # items() method
    print("3. items() - Get key-value pairs:")
    items = courses.items()
    print("   Key-Value pairs:")
    for code, info in items:
        print(f"      {code}: {info['name']}")
    print()

    # get() method with default
    print("4. get() - Safe access with default:")
    print(f"   AI301 grade: {courses.get('AI301', {}).get('grade', 'N/A')}")
    print(f"   PHYS101 grade: {courses.get('PHYS101', {}).get('grade', 'N/A')}\n")

    # setdefault() method
    print("5. setdefault() - Get or set default value:")
    courses.setdefault("PHYS101", {"name": "Physics", "credits": 4, "grade": "B"})
    print(f"   Added PHYS101: {courses['PHYS101']}\n")

    # copy() method
    print("6. copy() - Create shallow copy:")
    courses_copy = courses.copy()
    courses_copy["AI301"]["grade"] = "A+"  # This affects original too (shallow copy)
    print(f"   Original AI301 grade: {courses['AI301']['grade']}")
    print(f"   Copy AI301 grade: {courses_copy['AI301']['grade']}\n")

# ============================================================================
# 5. DICTIONARY ITERATION
# ============================================================================

def dictionary_iteration():
    """Different ways to iterate through dictionaries"""
    print("=== DICTIONARY ITERATION ===\n")

    student_grades = {
        "Alice": 95,
        "Bob": 87,
        "Charlie": 92,
        "Diana": 88,
        "Eve": 94
    }

    print("Sample data:")
    print(f"   {student_grades}\n")

    # Method 1: Iterate over keys (default)
    print("1. Iterating over keys:")
    for student in student_grades:
        print(f"   {student}: {student_grades[student]}")
    print()

    # Method 2: Iterate over keys explicitly
    print("2. Iterating over keys explicitly:")
    for student in student_grades.keys():
        print(f"   Student: {student}")
    print()

    # Method 3: Iterate over values
    print("3. Iterating over values:")
    for grade in student_grades.values():
        print(f"   Grade: {grade}")
    print()

    # Method 4: Iterate over key-value pairs
    print("4. Iterating over key-value pairs:")
    for student, grade in student_grades.items():
        print(f"   {student} scored {grade}")
    print()

    # Method 5: Enumerate with items()
    print("5. Using enumerate with items():")
    for i, (student, grade) in enumerate(student_grades.items(), 1):
        print(f"   {i}. {student}: {grade}")
    print()

# ============================================================================
# 6. DICTIONARY COMPREHENSIONS
# ============================================================================

def dictionary_comprehensions():
    """Advanced dictionary creation using comprehensions"""
    print("=== DICTIONARY COMPREHENSIONS ===\n")

    # Basic comprehension
    squares = {x: x**2 for x in range(1, 6)}
    print("1. Basic comprehension (squares):")
    print(f"   {squares}\n")

    # Comprehension with condition
    even_squares = {x: x**2 for x in range(1, 11) if x % 2 == 0}
    print("2. With condition (even squares):")
    print(f"   {even_squares}\n")

    # From existing dictionary
    grades = {"Alice": 95, "Bob": 67, "Charlie": 92, "Diana": 58}
    passing_grades = {name: grade for name, grade in grades.items() if grade >= 70}
    print("3. Filtering existing dictionary:")
    print(f"   Original: {grades}")
    print(f"   Passing: {passing_grades}\n")

    # Transform values
    grade_letters = {
        name: "A" if grade >= 90 else "B" if grade >= 80 else "C" if grade >= 70 else "F"
        for name, grade in grades.items()
    }
    print("4. Transform values (letter grades):")
    print(f"   {grade_letters}\n")

    # From two lists
    students = ["Alice", "Bob", "Charlie"]
    scores = [95, 87, 92]
    student_scores = {student: score for student, score in zip(students, scores)}
    print("5. From two lists using zip:")
    print(f"   Students: {students}")
    print(f"   Scores: {scores}")
    print(f"   Combined: {student_scores}\n")

# ============================================================================
# 7. NESTED DICTIONARIES
# ============================================================================

def nested_dictionaries():
    """Working with nested dictionaries"""
    print("=== NESTED DICTIONARIES ===\n")

    # Complex nested structure
    university = {
        "name": "Cyprus International University",
        "departments": {
            "engineering": {
                "programs": ["Computer Engineering", "AI Engineering"],
                "students": 450,
                "faculty": {
                    "Dr. Smith": {"courses": ["AI301", "ML401"], "office": "E201"},
                    "Dr. Johnson": {"courses": ["CS201", "CS301"], "office": "E202"}
                }
            },
            "business": {
                "programs": ["MBA", "Finance"],
                "students": 320,
                "faculty": {
                    "Dr. Brown": {"courses": ["BUS101", "FIN201"], "office": "B101"}
                }
            }
        }
    }

    print("1. Complex nested dictionary structure:")
    print(f"   University: {university['name']}")
    print(f"   Engineering students: {university['departments']['engineering']['students']}")
    print()

    # Accessing nested data
    print("2. Accessing nested data:")
    eng_faculty = university["departments"]["engineering"]["faculty"]
    for prof, info in eng_faculty.items():
        print(f"   {prof}: Office {info['office']}, Courses: {info['courses']}")
    print()

    # Safe nested access
    print("3. Safe nested access:")
    def safe_get(dictionary, *keys):
        """Safely get nested dictionary values"""
        for key in keys:
            if isinstance(dictionary, dict) and key in dictionary:
                dictionary = dictionary[key]
            else:
                return None
        return dictionary

    # Safe access examples
    office = safe_get(university, "departments", "engineering", "faculty", "Dr. Smith", "office")
    print(f"   Dr. Smith's office: {office}")

    invalid = safe_get(university, "departments", "medicine", "students")
    print(f"   Medicine students: {invalid}")
    print()

    # Modifying nested dictionaries
    print("4. Modifying nested dictionaries:")
    university["departments"]["engineering"]["faculty"]["Dr. Wilson"] = {
        "courses": ["AI401", "ML501"],
        "office": "E203"
    }
    print("   Added Dr. Wilson to engineering faculty")

    # Update nested values
    university["departments"]["engineering"]["students"] = 475
    print(f"   Updated engineering students: {university['departments']['engineering']['students']}")
    print()

# ============================================================================
# 8. PRACTICAL EXAMPLES
# ============================================================================

def practical_examples():
    """Real-world dictionary usage examples"""
    print("=== PRACTICAL EXAMPLES ===\n")

    # Example 1: Student Management System
    print("1. Student Management System:")
    students_db = {}

    def add_student(student_id, name, courses=None):
        students_db[student_id] = {
            "name": name,
            "courses": courses or [],
            "grades": {},
            "gpa": 0.0
        }

    def add_grade(student_id, course, grade):
        if student_id in students_db:
            students_db[student_id]["grades"][course] = grade
            # Calculate GPA
            grades = list(students_db[student_id]["grades"].values())
            students_db[student_id]["gpa"] = sum(grades) / len(grades)

    # Add students
    add_student("S001", "Fadhl Alsharif", ["AI301", "CS201", "MATH301"])
    add_student("S002", "Alice Johnson", ["CS201", "MATH301", "ENG101"])

    # Add grades
    add_grade("S001", "AI301", 95)
    add_grade("S001", "CS201", 88)
    add_grade("S001", "MATH301", 92)

    add_grade("S002", "CS201", 85)
    add_grade("S002", "MATH301", 90)
    add_grade("S002", "ENG101", 87)

    # Display results
    for student_id, info in students_db.items():
        print(f"   {student_id}: {info['name']}")
        print(f"      Courses: {info['courses']}")
        print(f"      Grades: {info['grades']}")
        print(f"      GPA: {info['gpa']:.2f}")
    print()

    # Example 2: Word Frequency Counter
    print("2. Word Frequency Counter:")
    text = "python is great python is powerful python is versatile"
    word_count = {}

    for word in text.split():
        word_count[word] = word_count.get(word, 0) + 1

    print(f"   Text: '{text}'")
    print("   Word frequencies:")
    for word, count in sorted(word_count.items()):
        print(f"      '{word}': {count}")
    print()

    # Example 3: Configuration Settings
    print("3. Configuration Settings:")
    config = {
        "database": {
            "host": "localhost",
            "port": 5432,
            "name": "university_db",
            "credentials": {
                "username": "admin",
                "password": "secret123"
            }
        },
        "api": {
            "version": "v1",
            "timeout": 30,
            "rate_limit": 1000
        },
        "features": {
            "debug_mode": True,
            "logging": True,
            "cache_enabled": False
        }
    }

    print("   Configuration loaded:")
    print(f"      Database: {config['database']['host']}:{config['database']['port']}")
    print(f"      API version: {config['api']['version']}")
    print(f"      Debug mode: {config['features']['debug_mode']}")
    print()

# ============================================================================
# 9. DICTIONARY PERFORMANCE AND BEST PRACTICES
# ============================================================================

def performance_and_best_practices():
    """Dictionary performance tips and best practices"""
    print("=== PERFORMANCE AND BEST PRACTICES ===\n")

    # Best Practice 1: Use get() for safe access
    print("1. Use get() for safe access:")
    user_data = {"name": "Fadhl", "age": 22}

    # Bad practice
    try:
        email = user_data["email"]  # KeyError if key doesn't exist
    except KeyError:
        email = "Not provided"

    # Good practice
    email = user_data.get("email", "Not provided")
    print(f"   Email: {email}\n")

    # Best Practice 2: Use setdefault() for initialization
    print("2. Use setdefault() for initialization:")
    grades_by_subject = {}

    # Adding grades
    subjects_grades = [
        ("Math", 95), ("Math", 87), ("Physics", 92),
        ("Physics", 88), ("Math", 91)
    ]

    for subject, grade in subjects_grades:
        grades_by_subject.setdefault(subject, []).append(grade)

    print("   Grades by subject:")
    for subject, grades in grades_by_subject.items():
        avg = sum(grades) / len(grades)
        print(f"      {subject}: {grades} (avg: {avg:.1f})")
    print()

    # Best Practice 3: Dictionary vs List for lookups
    print("3. Dictionary vs List for lookups:")
    import time

    # Create test data
    large_list = list(range(10000))
    large_dict = {i: f"value_{i}" for i in range(10000)}

    # List lookup (O(n))
    start_time = time.time()
    result = 9999 in large_list
    list_time = time.time() - start_time

    # Dictionary lookup (O(1))
    start_time = time.time()
    result = 9999 in large_dict
    dict_time = time.time() - start_time

    print(f"   List lookup time: {list_time:.6f} seconds")
    print(f"   Dict lookup time: {dict_time:.6f} seconds")
    print(f"   Dictionary is ~{list_time/dict_time:.0f}x faster\n")

    # Best Practice 4: Use collections.defaultdict for complex defaults
    print("4. Using collections.defaultdict:")
    from collections import defaultdict

    # Group students by grade level
    students = [
        ("Alice", "Freshman"), ("Bob", "Sophomore"), ("Charlie", "Freshman"),
        ("Diana", "Junior"), ("Eve", "Sophomore"), ("Frank", "Senior")
    ]

    # Using defaultdict
    students_by_level = defaultdict(list)
    for name, level in students:
        students_by_level[level].append(name)

    print("   Students by grade level:")
    for level, names in students_by_level.items():
        print(f"      {level}: {names}")
    print()

# ============================================================================
# 10. COMMON DICTIONARY PATTERNS
# ============================================================================

def common_patterns():
    """Common dictionary usage patterns"""
    print("=== COMMON DICTIONARY PATTERNS ===\n")

    # Pattern 1: Merging dictionaries
    print("1. Merging dictionaries:")
    dict1 = {"a": 1, "b": 2}
    dict2 = {"c": 3, "d": 4}
    dict3 = {"b": 20, "e": 5}  # Note: 'b' will be overwritten

    # Python 3.9+ method
    merged = dict1 | dict2 | dict3
    print(f"   Using | operator: {merged}")

    # Alternative method
    merged_alt = {**dict1, **dict2, **dict3}
    print(f"   Using ** unpacking: {merged_alt}")

    # Using update()
    merged_update = dict1.copy()
    merged_update.update(dict2)
    merged_update.update(dict3)
    print(f"   Using update(): {merged_update}\n")

    # Pattern 2: Inverting dictionaries
    print("2. Inverting dictionaries:")
    original = {"a": 1, "b": 2, "c": 3}
    inverted = {value: key for key, value in original.items()}
    print(f"   Original: {original}")
    print(f"   Inverted: {inverted}\n")

    # Pattern 3: Grouping data
    print("3. Grouping data:")
    from collections import defaultdict

    transactions = [
        {"type": "income", "amount": 1000, "category": "salary"},
        {"type": "expense", "amount": 200, "category": "food"},
        {"type": "expense", "amount": 100, "category": "transport"},
        {"type": "income", "amount": 500, "category": "freelance"},
        {"type": "expense", "amount": 150, "category": "food"}
    ]

    # Group by type
    by_type = defaultdict(list)
    for transaction in transactions:
        by_type[transaction["type"]].append(transaction)

    print("   Transactions by type:")
    for trans_type, trans_list in by_type.items():
        total = sum(t["amount"] for t in trans_list)
        print(f"      {trans_type}: {len(trans_list)} transactions, total: ${total}")
    print()

    # Pattern 4: Caching/Memoization
    print("4. Caching with dictionaries:")

    # Fibonacci with memoization
    fib_cache = {}

    def fibonacci(n):
        if n in fib_cache:
            return fib_cache[n]

        if n <= 1:
            result = n
        else:
            result = fibonacci(n-1) + fibonacci(n-2)

        fib_cache[n] = result
        return result

    # Calculate some Fibonacci numbers
    for i in range(10):
        print(f"   fib({i}) = {fibonacci(i)}")
    print(f"   Cache size: {len(fib_cache)}\n")

# ============================================================================
# MAIN FUNCTION TO RUN ALL EXAMPLES
# ============================================================================

def main():
    """Run all dictionary examples"""
    print("🐍 PYTHON DICTIONARIES - COMPLETE GUIDE")
    print("=" * 50)
    print()

    # Run all examples
    creating_dictionaries()
    accessing_elements()
    modifying_dictionaries()
    dictionary_methods()
    dictionary_iteration()
    dictionary_comprehensions()
    nested_dictionaries()
    practical_examples()
    performance_and_best_practices()
    common_patterns()

    print("🎉 Dictionary tutorial completed!")
    print("=" * 50)

if __name__ == "__main__":
    main()
