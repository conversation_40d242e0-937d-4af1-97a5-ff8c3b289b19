"""
Sample Course Data Generator
Creates realistic sample data for testing the course equivalency system
"""

import pandas as pd
import json
import random

def generate_sample_courses():
    """Generate sample course data for all three universities"""
    
    # Sample courses for Computer Engineering
    comp_eng_courses = [
        # Programming Courses
        {"base_name": "Introduction to Programming", "codes": ["COMP101", "CSE101", "CS101"], 
         "credits": [3, 3, 4], "descriptions": [
             "Basic programming concepts using Python and C++",
             "Fundamentals of computer programming and problem solving",
             "Introduction to programming logic and algorithm design"
         ]},
        {"base_name": "Data Structures and Algorithms", "codes": ["COMP201", "CSE201", "CS201"], 
         "credits": [4, 4, 3], "descriptions": [
             "Implementation of data structures and algorithm analysis",
             "Advanced data structures, sorting and searching algorithms",
             "Data organization and algorithmic problem solving"
         ]},
        {"base_name": "Object Oriented Programming", "codes": ["COMP202", "CSE202", "CS202"], 
         "credits": [3, 4, 3], "descriptions": [
             "Object-oriented design principles using Java and C++",
             "Advanced programming with object-oriented methodologies",
             "OOP concepts, inheritance, polymorphism, and encapsulation"
         ]},
        
        # Mathematics Courses
        {"base_name": "Calculus I", "codes": ["MATH101", "MATH101", "MAT101"], 
         "credits": [4, 4, 4], "descriptions": [
             "Differential and integral calculus for engineering",
             "Limits, derivatives, and applications of calculus",
             "Mathematical foundations for engineering applications"
         ]},
        {"base_name": "Linear Algebra", "codes": ["MATH201", "MATH201", "MAT201"], 
         "credits": [3, 3, 3], "descriptions": [
             "Matrix operations, vector spaces, and linear transformations",
             "Linear algebra concepts for computer science applications",
             "Vectors, matrices, and systems of linear equations"
         ]},
        {"base_name": "Discrete Mathematics", "codes": ["MATH301", "MATH301", "MAT301"], 
         "credits": [3, 3, 4], "descriptions": [
             "Logic, sets, relations, and graph theory",
             "Mathematical structures for computer science",
             "Combinatorics, probability, and discrete structures"
         ]},
        
        # Systems Courses
        {"base_name": "Computer Organization", "codes": ["COMP301", "CSE301", "CS301"], 
         "credits": [4, 4, 3], "descriptions": [
             "Computer architecture and assembly language programming",
             "Digital logic design and computer organization",
             "Hardware-software interface and system design"
         ]},
        {"base_name": "Operating Systems", "codes": ["COMP401", "CSE401", "CS401"], 
         "credits": [3, 4, 4], "descriptions": [
             "Process management, memory management, and file systems",
             "Operating system concepts and implementation",
             "System programming and OS design principles"
         ]},
        {"base_name": "Database Systems", "codes": ["COMP302", "CSE302", "CS302"], 
         "credits": [3, 3, 4], "descriptions": [
             "Database design, SQL, and database management systems",
             "Relational databases and query optimization",
             "Data modeling and database implementation"
         ]},
        
        # Networks and Security
        {"base_name": "Computer Networks", "codes": ["COMP402", "CSE402", "CS402"], 
         "credits": [3, 4, 3], "descriptions": [
             "Network protocols, TCP/IP, and network programming",
             "Computer networking and distributed systems",
             "Network architecture and communication protocols"
         ]},
        {"base_name": "Cybersecurity", "codes": ["COMP501", "CSE501", "CS501"], 
         "credits": [3, 3, 3], "descriptions": [
             "Information security, cryptography, and network security",
             "Computer security and risk management",
             "Security protocols and vulnerability assessment"
         ]},
        
        # Software Engineering
        {"base_name": "Software Engineering", "codes": ["COMP403", "CSE403", "CS403"], 
         "credits": [4, 3, 4], "descriptions": [
             "Software development lifecycle and project management",
             "Software design patterns and engineering principles",
             "Requirements analysis and software testing"
         ]},
    ]
    
    # Sample courses for Artificial Intelligence
    ai_courses = [
        {"base_name": "Introduction to Artificial Intelligence", "codes": ["AI101", "CSE401", "CS401"], 
         "credits": [3, 4, 3], "descriptions": [
             "Fundamentals of AI, search algorithms, and knowledge representation",
             "Introduction to artificial intelligence and machine learning",
             "AI concepts, problem solving, and intelligent agents"
         ]},
        {"base_name": "Machine Learning", "codes": ["AI201", "CSE501", "CS501"], 
         "credits": [4, 4, 4], "descriptions": [
             "Supervised and unsupervised learning algorithms",
             "Statistical learning and pattern recognition",
             "ML algorithms, neural networks, and deep learning"
         ]},
        {"base_name": "Deep Learning", "codes": ["AI301", "CSE601", "CS601"], 
         "credits": [3, 3, 4], "descriptions": [
             "Neural networks, CNNs, RNNs, and deep learning frameworks",
             "Advanced neural network architectures",
             "Deep learning theory and practical applications"
         ]},
        {"base_name": "Natural Language Processing", "codes": ["AI302", "CSE602", "CS602"], 
         "credits": [3, 4, 3], "descriptions": [
             "Text processing, language models, and NLP applications",
             "Computational linguistics and language understanding",
             "NLP techniques, sentiment analysis, and text mining"
         ]},
        {"base_name": "Computer Vision", "codes": ["AI303", "CSE603", "CS603"], 
         "credits": [3, 3, 3], "descriptions": [
             "Image processing, feature extraction, and object recognition",
             "Computer vision algorithms and applications",
             "Visual perception and image analysis techniques"
         ]},
        {"base_name": "Robotics", "codes": ["AI401", "CSE701", "CS701"], 
         "credits": [4, 3, 4], "descriptions": [
             "Robot kinematics, control systems, and autonomous navigation",
             "Robotics engineering and intelligent systems",
             "Robot programming and sensor integration"
         ]},
        {"base_name": "Data Mining", "codes": ["AI202", "CSE502", "CS502"], 
         "credits": [3, 4, 3], "descriptions": [
             "Data preprocessing, clustering, and association rules",
             "Knowledge discovery and data analytics",
             "Big data analysis and mining techniques"
         ]},
        {"base_name": "Expert Systems", "codes": ["AI402", "CSE702", "CS702"], 
         "credits": [3, 3, 3], "descriptions": [
             "Knowledge-based systems and inference engines",
             "Expert system design and implementation",
             "Rule-based systems and knowledge engineering"
         ]},
    ]
    
    universities = ['CIU', 'EMU', 'NEAR_EAST']
    programs = ['computer_engineering', 'artificial_intelligence']
    
    all_courses = []
    
    # Generate courses for each university and program
    for uni_idx, university in enumerate(universities):
        for program in programs:
            course_list = comp_eng_courses if program == 'computer_engineering' else ai_courses
            
            for course in course_list:
                course_data = {
                    'university': university,
                    'program': program,
                    'course_code': course['codes'][uni_idx],
                    'course_name': course['base_name'],
                    'credits': course['credits'][uni_idx],
                    'description': course['descriptions'][uni_idx]
                }
                all_courses.append(course_data)
    
    return all_courses

def save_sample_data():
    """Generate and save sample course data"""
    courses = generate_sample_courses()
    
    # Save to CSV
    df = pd.DataFrame(courses)
    df.to_csv('university_courses.csv', index=False)
    
    # Save to JSON
    with open('university_courses.json', 'w', encoding='utf-8') as f:
        json.dump(courses, f, indent=2, ensure_ascii=False)
    
    print(f"Generated {len(courses)} sample courses")
    print(f"Universities: {df['university'].value_counts().to_dict()}")
    print(f"Programs: {df['program'].value_counts().to_dict()}")
    
    return df

if __name__ == "__main__":
    df = save_sample_data()
    print("\nSample data generated successfully!")
    print("\nFirst few courses:")
    print(df.head(10).to_string())
